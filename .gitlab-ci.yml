image: node:lts

stages:
  - release
  - docs
  - deploy

.git-checkout-master: &git-checkout-master
  - git config --global user.name "${GITLAB_USER_NAME}"
  - git config --global user.email "${GITLAB_USER_EMAIL}"
  - git remote set-url origin https://root:$GITLAB_TOKEN@$CI_SERVER_HOST/$CI_PROJECT_PATH.git
  - git remote -v
  - git fetch origin "$CI_DEFAULT_BRANCH"
  - git checkout -B "$CI_DEFAULT_BRANCH" "origin/$CI_DEFAULT_BRANCH"

.before_script: &before_script
  - echo "确定GITLAB_TOKEN存在 $GITLAB_TOKEN"
  - echo "确定NPM_TOKEN存在 $NPM_TOKEN"
  - npm install -g pnpm@latest --registry https://registry.npmmirror.com
  - pnpm install --registry https://registry.npmmirror.com
  - echo "https://root:$GITLAB_TOKEN@$CI_SERVER_HOST/$CI_PROJECT_PATH.git HEAD:master"
  - *git-checkout-master
  - git tag -l | xargs git tag -d  # 删除所有本地 tag
  - git fetch --tags  # 从远程重新获取所有 tag
  - git rev-parse --short HEAD
  - npm config set //registry.npmjs.org/:_authToken $NPM_TOKEN

.after_script: &after_script
  - git status

release-major:
  stage: release
  before_script: *before_script
  after_script: *after_script
  script:
    - pnpm release major --ci
  rules:
    - if: '$CI_COMMIT_TAG =~ /^major-\d{8}-\d{4}$/'
      when: on_success

release-minor:
  stage: release
  before_script: *before_script
  after_script: *after_script
  script:
    - pnpm release minor --ci
  rules:
    - if: '$CI_COMMIT_TAG =~ /^minor-\d{8}-\d{4}$/'
      when: on_success

release-patch:
  stage: release
  before_script: *before_script
  after_script: *after_script
  script:
    - pnpm release patch --ci
  rules:
    - if: '$CI_COMMIT_TAG =~ /^patch-\d{8}-\d{4}$/'
      when: on_success

release-alpha:
  stage: release
  before_script: *before_script
  after_script: *after_script
  script:
    - pnpm release prerelease --preReleaseId=alpha --ci
  rules:
    - if: '$CI_COMMIT_TAG =~ /^alpha-\d{8}-\d{4}$/'
      when: on_success

pages:
  stage: docs
  publish: docs
  before_script: *git-checkout-master
  script:
    - npm config set registry https://registry.npmmirror.com
    - npm install -g pnpm@latest
    - pnpm install
    - pnpm doc
  artifacts:
    paths:
      - docs/
  rules:
    - if: '$CI_COMMIT_REF_NAME == "master"'
      when: on_success
    - if: '$CI_COMMIT_TAG =~ /^docs-\d{8}-\d{4}$/'
      when: on_success

deploy-pages:
  stage: deploy
  needs:
    - pages
  script:
    - echo "部署文档到GitLab Pages"
  rules:
    - if: '$CI_COMMIT_REF_NAME == "master"'
      when: on_success
    - if: '$CI_COMMIT_TAG =~ /^docs-\d{8}-\d{4}$/'
      when: on_success
    - if: '$CI_COMMIT_TAG =~ /^alpha-\d{8}-\d{4}$/'
      when: on_success
