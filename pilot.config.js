// 测试子命令功能的配置
export default {
  defaultCommands: {
    start: {
      cmd: 'echo',
      args: ['启动', '{{environment.name}}', '环境'],
    },
    build: {
      cmd: 'echo',
      args: ['构建', '{{environment.name}}', '环境'],
    },
  },

  environments: [
    {
      name: 'dev',
      value: 'dev',
      description: '开发环境',
      mode: 'development',
    },
    {
      name: 'prod',
      value: 'prod',
      description: '生产环境',
      mode: 'production',
    },
  ],
}
