{"name": "@seakoi/pilot-cli", "version": "0.0.1", "description": "Modern TypeScript CLI tool for frontend development environment management. Supports interactive environment selection and flexible configuration, compatible with any build tool (Vite, Webpack, Rollup, etc.)", "main": "src/index.ts", "type": "module", "bin": {"pilot-cli": "dist/index.js"}, "engines": {"node": ">=22.0.0"}, "files": ["dist/", "README.md"], "scripts": {"dev": "tsdown --watch", "build": "tsdown", "typecheck": "tsc --noEmit", "lint": "eslint . --fix", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "prepare": "husky", "doc": "scripts/build-docs.sh", "release": "release-it"}, "dependencies": {"chalk": "^5.5.0", "commander": "^14.0.0", "inquirer": "^9.3.7", "ora": "^8.2.0"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@release-it/conventional-changelog": "^10.0.1", "@stylistic/eslint-plugin": "^5.2.3", "@types/inquirer": "^9.0.9", "@types/node": "^24.2.1", "@typescript-eslint/eslint-plugin": "^8.39.1", "@typescript-eslint/parser": "^8.39.1", "@vitest/coverage-v8": "^3.2.4", "eslint": "^9.33.0", "github-markdown-css": "^5.8.1", "globals": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.5", "markdown-it-cli": "^1.0.0", "release-it": "^19.0.4", "tsdown": "^0.14.1", "typescript": "^5.9.2", "vitest": "^3.2.4"}, "packageManager": "pnpm@10.0.0", "repository": {"type": "git", "url": "*********************:seakoi/pilot-cli.git"}, "publishConfig": {"access": "public"}, "keywords": ["cli", "frontend", "development", "environment", "build-tool", "vite", "webpack", "rollup", "typescript", "interactive", "configuration"], "author": "cola", "license": "ISC", "lint-staged": {"*.{ts,tsx}": ["eslint --fix"]}}