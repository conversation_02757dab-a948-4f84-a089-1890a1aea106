# Dependencies
node_modules/
.pnpm-store/

# Build outputs
dist/
*.tsbuildinfo

# Generated documentation
docs/

# Environment files
.env
.env.local
.env.*.local
.current-env.json

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
npm-debug.log*
pnpm-debug.log*

# Coverage
coverage/
.nyc_output/

# Temporary files
*.tmp
*.temp
.cache/

# Package manager lock files (keep pnpm-lock.yaml)
package-lock.json
yarn.lock

.kiro
.gemini
.gitlab-ci-local/


examples
