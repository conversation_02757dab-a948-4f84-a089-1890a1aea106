# Pilot Cli

🚀 现代化的 TypeScript CLI 工具，用于前端开发环境管理。支持交互式环境选择和灵活的配置定制，兼容任何构建工具（Vite、Webpack、Rollup 等）。

## ✨ 特性

- 🎯 **交互式环境选择** - 通过友好的命令行界面选择开发环境
- ⚡ **快速启动** - 一键启动不同环境的开发服务器
- 🔧 **灵活配置** - 支持自定义环境和命令配置
- 🛠️ **构建工具无关** - 兼容 Vite、Webpack、Rollup、Parcel 等任何构建工具
- 📦 **零配置使用** - 开箱即用，内置常用环境配置
- 🎨 **TypeScript 支持** - 完整的 TypeScript 类型支持

## 📦 安装

### 全局安装

```bash
npm install -g @seakoi/pilot-cli
```

### 项目内安装

```bash
# npm
npm install --save-dev @seakoi/pilot-cli

# yarn
yarn add -D @seakoi/pilot-cli

# pnpm
pnpm add -D @seakoi/pilot-cli
```

## ✨ 功能特性

- 🎯 **多子命令支持**: `start` 启动开发服务器，`build` 构建项目
- 🔧 **灵活配置**: 支持为不同子命令配置不同的执行命令
- 🪝 **Hook 系统**: 在命令执行前后运行自定义脚本或函数
- 🌍 **多环境管理**: 轻松切换开发、测试、生产等环境
- 📝 **模板变量**: 在命令中使用环境变量模板
- 🔄 **向后兼容**: 完全兼容旧版本配置
- 💻 **交互式界面**: 友好的命令行交互体验
- ⚡ **零配置启动**: 开箱即用的默认配置

## 🚀 使用方法

### 子命令

Pilot CLI 支持多种子命令：

- `start` - 启动开发服务器（默认）
- `build` - 构建项目

### 启动开发服务器

#### 交互式模式

```bash
# 交互式选择环境
pilot-cli start
# 或简写（默认为 start）
pilot-cli
```

你会看到类似这样的界面：

```
? 请选择环境: (Use arrow keys)
❯ dev - 开发环境
  test - 测试环境
  hotfix - 热修复环境
  prod - 生产环境
```

#### 直接指定环境

```bash
# 启动开发环境
pilot-cli start --api=dev
pilot-cli --api=dev  # 简写形式

# 启动测试环境
pilot-cli start --api=test

# 启动生产环境
pilot-cli start --api=prod
```

### 构建项目

#### 交互式模式

```bash
pilot-cli build
```

#### 直接指定环境

```bash
# 构建生产环境
pilot-cli build --api=prod

# 构建测试环境
pilot-cli build --api=test
```

### 显示详细输出

```bash
pilot-cli start --api=dev --verbose
pilot-cli build --api=prod --verbose
```

### 查看帮助信息

```bash
# 查看版本
pilot-cli --version

# 查看帮助
pilot-cli --help

# 查看子命令帮助
pilot-cli start --help
pilot-cli build --help
```

## ⚙️ 自定义配置

### 创建配置文件

在项目根目录创建 `pilot.config.ts` 文件来自定义环境配置：

```typescript
// pilot.config.ts
import type { Config } from '@seakoi/pilot-cli'

export default {
  environments: [
    {
      name: 'dev',
      value: 'development',
      description: '开发环境',
      mode: 'development',
      command: {
        cmd: 'npx',
        args: ['vite', '--mode', '{{environment.value}}', '--host'],
      },
    },
    {
      name: 'prod',
      value: 'production',
      description: '生产环境',
      mode: 'production',
      command: {
        cmd: 'npx',
        args: ['vite', 'build', '--mode', '{{environment.value}}'],
      },
    },
  ],
} satisfies Config
```

### 使用 defaultCommand 简化配置

如果多个环境使用相同的命令，可以使用 `defaultCommand` 来避免重复：

```typescript
// pilot.config.ts
import type { Config } from '@seakoi/pilot-cli'

export default {
  // 默认命令 - 所有环境都会使用这个命令（除非环境自己定义了 command）
  defaultCommand: {
    cmd: 'npx',
    args: ['vite', '--mode', '{{environment.value}}', '--host'],
  },

  environments: [
    {
      name: 'dev',
      value: 'development',
      description: '开发环境',
      mode: 'development',
      // 不需要定义 command，会自动使用 defaultCommand
    },
    {
      name: 'test',
      value: 'test',
      description: '测试环境',
      mode: 'test',
      // 不需要定义 command，会自动使用 defaultCommand
    },
    {
      name: 'prod',
      value: 'production',
      description: '生产环境',
      mode: 'production',
      // 生产环境需要特殊命令，覆盖 defaultCommand
      command: {
        cmd: 'npx',
        args: ['vite', 'build', '--mode', '{{environment.value}}'],
      },
    },
  ],
} satisfies Config
```

### 配置文件格式

#### 环境配置 (Environment)

```typescript
interface Environment {
  name: string        // 环境名称，用于显示和选择
  value: string       // 环境值，用于传递给命令
  description: string // 环境描述，显示在选择菜单中
  mode: string        // 环境模式，通常对应 Vite 的 mode
  command?: Command   // 可选：该环境专用的命令
}
```

#### 命令配置 (Command)

```typescript
interface Command {
  cmd: string      // 要执行的命令
  args: string[]   // 命令参数数组
}
```

#### 完整配置 (Config)

```typescript
interface Config {
  defaultCommand?: Command    // 可选：默认命令
  environments: Environment[] // 环境列表
}
```

### 模板变量

命令参数支持以下模板变量：

- `{{environment.value}}` - 环境值
- `{{environment.name}}` - 环境名称
- `{{environment.mode}}` - 环境模式

#### 示例

```typescript
{
  cmd: 'npx',
  args: [
    'vite',
    '--mode', '{{environment.value}}',    // 会被替换为 'development'
    '--host', '{{environment.name}}',     // 会被替换为 'dev'
    '--port', '3000'
  ]
}
```

### 配置文件查找顺序

工具会按以下顺序查找配置文件：

1. `pilot.config.ts`
2. `pilot.config.js`
3. `pilot.config.mjs`

如果没有找到配置文件，会使用内置的默认配置。

### 默认配置

如果不创建配置文件，工具会使用以下默认配置：

```typescript
{
  defaultCommand: {
    cmd: 'npx',
    args: ['vite', '--mode', '{{environment.value}}', '--host'],
  },
  environments: [
    { name: 'dev', value: 'dev', description: '开发环境', mode: 'development' },
    { name: 'test', value: 'test', description: '测试环境', mode: 'test' },
    { name: 'hotfix', value: 'hotfix', description: '热修复环境', mode: 'hotfix' },
    { name: 'prod', value: 'prod', description: '生产环境', mode: 'production' },
  ],
}
```

## 🪝 Hooks 功能

Pilot CLI 支持在命令执行前后运行自定义的 hooks，让你可以在启动环境前进行准备工作或在启动后执行清理任务。

### Hook 类型

- **command**: 执行 shell 命令
- **function**: 执行 JavaScript 函数

### Hook 级别

- **全局 hooks**: 在所有环境中都会执行
- **环境 hooks**: 只在特定环境中执行

### 配置示例

```typescript
// pilot.config.ts
import type { Config } from '@seakoi/pilot-cli'

const config: Config = {
  // 全局 pre-hooks：在所有环境启动前执行
  globalPreHooks: [
    {
      name: 'install-deps',
      description: '安装依赖',
      type: 'command',
      cmd: 'npm',
      args: ['install'],
    },
  ],

  // 全局 post-hooks：在所有环境启动后执行
  globalPostHooks: [
    {
      name: 'notify',
      description: '启动完成通知',
      type: 'function',
      fn: async (environment, config) => {
        console.log(`🎉 ${environment.name} 环境已启动！`)
      },
    },
  ],

  environments: [
    {
      name: 'dev',
      value: 'dev',
      description: '开发环境',
      mode: 'development',
      // 开发环境特定的 hooks
      preHooks: [
        {
          name: 'clear-cache',
          description: '清理缓存',
          type: 'command',
          cmd: 'rm',
          args: ['-rf', 'node_modules/.cache'],
        },
      ],
    },
  ],
}

export default config
```

### 模板变量

在 command 类型的 hook 中，你可以使用以下模板变量：

- `{{environment.name}}`: 环境名称
- `{{environment.value}}`: 环境值
- `{{environment.mode}}`: 环境模式
- `{{environment.description}}`: 环境描述

## 📝 配置示例

### Vite 项目

```typescript
// pilot.config.ts
export default {
  // 支持不同子命令的默认配置
  defaultCommands: {
    start: {
      cmd: 'npx',
      args: ['vite', '--mode', '{{environment.value}}', '--host'],
    },
    build: {
      cmd: 'npx',
      args: ['vite', 'build', '--mode', '{{environment.value}}'],
    },
  },
  environments: [
    { name: 'dev', value: 'development', description: '开发环境', mode: 'development' },
    { name: 'prod', value: 'production', description: '生产环境', mode: 'production' },
  ],
}
```

### 向后兼容配置

```typescript
// pilot.config.ts - 仍然支持旧的配置方式
export default {
  defaultCommand: {
    cmd: 'npx',
    args: ['vite', '--mode', '{{environment.value}}'],
  },
  environments: [
    { name: 'dev', value: 'development', description: '开发环境', mode: 'development' },
    { name: 'prod', value: 'production', description: '生产环境', mode: 'production' },
  ],
}
```

### 完整的子命令配置

```typescript
// pilot.config.ts
export default {
  // 默认命令配置，支持不同的子命令
  defaultCommands: {
    start: {
      cmd: 'npx',
      args: ['vite', '--mode', '{{environment.value}}', '--host'],
    },
    build: {
      cmd: 'npx',
      args: ['vite', 'build', '--mode', '{{environment.value}}'],
    },
  },

  environments: [
    {
      name: 'dev',
      value: 'development',
      description: '开发环境',
      mode: 'development',
      // 为不同子命令配置不同的命令
      commands: {
        start: {
          cmd: 'npx',
          args: ['vite', '--mode', 'development', '--host', '--port', '3000'],
        },
        build: {
          cmd: 'npx',
          args: ['vite', 'build', '--mode', 'development'],
        },
      },
    },
    {
      name: 'prod',
      value: 'production',
      description: '生产环境',
      mode: 'production',
      commands: {
        start: {
          cmd: 'npx',
          args: ['vite', 'preview', '--host', '--port', '4173'],
        },
        build: {
          cmd: 'npx',
          args: ['vite', 'build', '--mode', 'production'],
        },
      },
    },
  ],
}
```

### Webpack 项目

```typescript
// pilot.config.ts
export default {
  environments: [
    {
      name: 'dev',
      value: 'development',
      description: '开发环境',
      mode: 'development',
      command: { cmd: 'npx', args: ['webpack', 'serve', '--mode', '{{environment.value}}'] },
    },
    {
      name: 'build',
      value: 'production',
      description: '构建生产版本',
      mode: 'production',
      command: { cmd: 'npx', args: ['webpack', '--mode', '{{environment.value}}'] },
    },
  ],
}
```

### Next.js 项目

```typescript
// pilot.config.ts
export default {
  environments: [
    {
      name: 'dev',
      value: 'development',
      description: '开发环境',
      mode: 'development',
      command: { cmd: 'npm', args: ['run', 'dev'] },
    },
    {
      name: 'build',
      value: 'production',
      description: '构建项目',
      mode: 'production',
      command: { cmd: 'npm', args: ['run', 'build'] },
    },
    {
      name: 'start',
      value: 'production',
      description: '启动生产服务器',
      mode: 'production',
      command: { cmd: 'npm', args: ['run', 'start'] },
    },
  ],
}
```

### 多端口开发

```typescript
// pilot.config.ts
export default {
  environments: [
    {
      name: 'dev',
      value: 'development',
      description: '开发环境 (端口 3000)',
      mode: 'development',
      command: { cmd: 'npx', args: ['vite', '--port', '3000'] },
    },
    {
      name: 'dev-mobile',
      value: 'development',
      description: '移动端开发 (端口 3001)',
      mode: 'development',
      command: { cmd: 'npx', args: ['vite', '--port', '3001', '--host'] },
    },
  ],
}
```

### 自定义构建命令

```typescript
// pilot.config.ts
export default {
  environments: [
    {
      name: 'dev',
      value: 'development',
      description: '开发环境',
      mode: 'development',
      command: { cmd: 'npm', args: ['run', 'dev'] },
    },
    {
      name: 'build',
      value: 'production',
      description: '构建生产版本',
      mode: 'production',
      command: { cmd: 'npm', args: ['run', 'build'] },
    },
    {
      name: 'preview',
      value: 'production',
      description: '预览生产版本',
      mode: 'production',
      command: { cmd: 'npm', args: ['run', 'preview'] },
    },
  ],
}
```

## 🔧 故障排除

### 常见问题

**Q: 提示 "不支持的环境" 错误？**

A: 检查你输入的环境名称是否在配置文件的 `environments` 数组中。使用 `pilot-cli --help` 查看可用环境。

**Q: 配置文件不生效？**

A: 确保配置文件名称正确（`pilot.config.ts`），并且文件位于项目根目录。

**Q: 命令执行失败？**

A: 检查 `command.cmd` 和 `command.args` 配置是否正确，确保相关依赖已安装。

### 调试模式

如果遇到问题，可以查看详细的错误信息来调试配置。

## 📄 许可证

ISC
