{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": false, "strict": true, "noImplicitAny": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "outDir": "./dist", "rootDir": "./src", "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}