/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  describe, test, expect, vi, beforeEach,
} from 'vitest'
import { HookExecutor } from '@/lib/hook-executor'
import { CommandExecutor } from '@/lib/command-executor'
import type { Hook, Environment, Config } from '@/config'

// Mock CommandExecutor
vi.mock('@/lib/command-executor')

describe('HookExecutor', () => {
  let hookExecutor: HookExecutor
  let mockCommandExecutor: any

  const mockEnvironment: Environment = {
    name: 'test',
    value: 'test',
    description: '测试环境',
    mode: 'test',
  }

  const mockConfig: Config = { environments: [mockEnvironment] }

  beforeEach(() => {
    vi.clearAllMocks()

    mockCommandExecutor = { execute: vi.fn().mockResolvedValue(0) }

    vi.mocked(CommandExecutor).mockImplementation(() => mockCommandExecutor)

    hookExecutor = new HookExecutor({ verbose: true })
  })

  describe('executeHook', () => {
    test('should execute command hook successfully', async () => {
      const hook: Hook = {
        name: 'test-command',
        description: '测试命令',
        type: 'command',
        cmd: 'echo',
        args: ['hello', '{{environment.name}}'],
      }

      await hookExecutor.executeHook(hook, mockEnvironment, mockConfig)

      expect(mockCommandExecutor.execute).toHaveBeenCalledWith(
        'echo',
        ['hello', 'test'],
        expect.objectContaining({
          description: '测试命令',
          env: expect.objectContaining({ NODE_ENV: 'test' }),
        }),
      )
    })

    test('should execute function hook successfully', async () => {
      const mockFn = vi.fn().mockResolvedValue(undefined)
      const hook: Hook = {
        name: 'test-function',
        description: '测试函数',
        type: 'function',
        fn: mockFn,
      }

      await hookExecutor.executeHook(hook, mockEnvironment, mockConfig)

      expect(mockFn).toHaveBeenCalledWith(mockEnvironment, mockConfig)
    })

    test('should throw error for command hook without cmd', async () => {
      const hook: Hook = {
        type: 'command',
        // cmd 缺失
      }

      await expect(
        hookExecutor.executeHook(hook, mockEnvironment, mockConfig),
      ).rejects.toThrow('Hook 执行失败')
    })

    test('should throw error for function hook without fn', async () => {
      const hook: Hook = {
        type: 'function',
        // fn 缺失
      }

      await expect(
        hookExecutor.executeHook(hook, mockEnvironment, mockConfig),
      ).rejects.toThrow('Hook 执行失败')
    })

    test('should throw error for unsupported hook type', async () => {
      const hook = { type: 'unsupported' } as any

      await expect(
        hookExecutor.executeHook(hook, mockEnvironment, mockConfig),
      ).rejects.toThrow('Hook 执行失败')
    })
  })

  describe('executeHooks', () => {
    test('should execute multiple hooks in sequence', async () => {
      const hooks: Hook[] = [
        {
          name: 'hook1',
          type: 'command',
          cmd: 'echo',
          args: ['hook1'],
        },
        {
          name: 'hook2',
          type: 'command',
          cmd: 'echo',
          args: ['hook2'],
        },
      ]

      await hookExecutor.executeHooks(hooks, mockEnvironment, mockConfig, 'pre')

      expect(mockCommandExecutor.execute).toHaveBeenCalledTimes(2)
      expect(mockCommandExecutor.execute).toHaveBeenNthCalledWith(
        1,
        'echo',
        ['hook1'],
        expect.any(Object),
      )
      expect(mockCommandExecutor.execute).toHaveBeenNthCalledWith(
        2,
        'echo',
        ['hook2'],
        expect.any(Object),
      )
    })

    test('should handle empty hooks array', async () => {
      await hookExecutor.executeHooks([], mockEnvironment, mockConfig, 'pre')
      expect(mockCommandExecutor.execute).not.toHaveBeenCalled()
    })

    test('should handle undefined hooks', async () => {
      await hookExecutor.executeHooks(undefined as any, mockEnvironment, mockConfig, 'pre')
      expect(mockCommandExecutor.execute).not.toHaveBeenCalled()
    })
  })

  describe('template variable replacement', () => {
    test('should replace all template variables in command args', async () => {
      const hook: Hook = {
        type: 'command',
        cmd: 'echo',
        args: [
          '{{environment.name}}',
          '{{environment.value}}',
          '{{environment.mode}}',
          '{{environment.description}}',
        ],
      }

      await hookExecutor.executeHook(hook, mockEnvironment, mockConfig)

      expect(mockCommandExecutor.execute).toHaveBeenCalledWith(
        'echo',
        ['test', 'test', 'test', '测试环境'],
        expect.any(Object),
      )
    })
  })
})
