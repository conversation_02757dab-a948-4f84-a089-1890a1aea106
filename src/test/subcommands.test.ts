/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  describe, test, expect, vi, beforeEach,
} from 'vitest'
import { SubcommandManager } from '@/lib/subcommands'
import { ConfigManager, EnvironmentManager } from '@/lib'

// Mock dependencies
vi.mock('@/lib/config')
vi.mock('@/lib/environments')
vi.mock('@/lib/command-executor')
vi.mock('chalk', () => ({
  default: {
    red: vi.fn((text) => text),
    yellow: vi.fn((text) => text),
    blue: vi.fn((text) => text),
    green: vi.fn((text) => text),
    gray: vi.fn((text) => text),
  },
}))

describe('SubcommandManager', () => {
  let subcommandManager: SubcommandManager
  let mockConfigManager: any
  let mockEnvironmentManager: any

  beforeEach(() => {
    vi.clearAllMocks()

    // Mock ConfigManager
    mockConfigManager = {
      load: vi.fn().mockResolvedValue({
        environments: [
          {
            name: 'dev', value: 'dev', description: '开发环境', mode: 'development',
          },
          {
            name: 'prod', value: 'prod', description: '生产环境', mode: 'production',
          },
        ],
        defaultCommands: {
          start: { cmd: 'npx', args: ['vite', '--mode', '{{environment.value}}'] },
          build: { cmd: 'npx', args: ['vite', 'build', '--mode', '{{environment.value}}'] },
        },
      }),
    }
    vi.mocked(ConfigManager).load = mockConfigManager.load

    // Mock EnvironmentManager
    mockEnvironmentManager = {
      getByValue: vi.fn(),
      getValues: vi.fn().mockReturnValue(['dev', 'prod']),
      getAll: vi.fn().mockReturnValue([
        {
          name: 'dev', value: 'dev', description: '开发环境', mode: 'development',
        },
        {
          name: 'prod', value: 'prod', description: '生产环境', mode: 'production',
        },
      ]),
    }
    vi.mocked(EnvironmentManager).mockImplementation(() => mockEnvironmentManager)

    subcommandManager = new SubcommandManager()
  })

  describe('getSubcommandDescription', () => {
    test('should return correct descriptions for subcommands', () => {
      // 通过反射访问私有方法进行测试
      const manager = subcommandManager as any
      expect(manager.getSubcommandDescription('start')).toBe('启动')
      expect(manager.getSubcommandDescription('build')).toBe('构建')
      expect(manager.getSubcommandDescription('unknown' as any)).toBe('执行')
    })
  })

  describe('parse', () => {
    test('should handle backward compatibility with api flag only', () => {
      const argv = ['node', 'pilot-cli', '--api=dev']
      const parseSpy = vi.spyOn(subcommandManager['program'], 'parse').mockImplementation(() => {})

      subcommandManager.parse(argv)

      // Should insert 'start' subcommand for backward compatibility
      expect(parseSpy).toHaveBeenCalledWith(['node', 'pilot-cli', 'start', '--api=dev'])

      parseSpy.mockRestore()
    })

    test('should handle normal parsing when subcommand is present', () => {
      const argv = ['node', 'pilot-cli', 'build', '--api=prod']
      const parseSpy = vi.spyOn(subcommandManager['program'], 'parse').mockImplementation(() => {})

      subcommandManager.parse(argv)

      expect(parseSpy).toHaveBeenCalledWith(argv)

      parseSpy.mockRestore()
    })
  })
})
