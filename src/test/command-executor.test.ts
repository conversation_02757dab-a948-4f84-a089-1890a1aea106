/* eslint-disable @typescript-eslint/no-explicit-any */

import {
  describe, test, expect, beforeEach, vi,
} from 'vitest'
import { CommandExecutor, executeViteCommand } from '@/lib'
import { spawn } from 'child_process'

// Mock child_process
vi.mock('child_process', () => ({ spawn: vi.fn() }))

const mockSpawn = vi.mocked(spawn)

// Mock chalk and ora
vi.mock('chalk', () => ({
  default: {
    blue: vi.fn((text) => text),
    green: vi.fn((text) => text),
    red: vi.fn((text) => text),
    yellow: vi.fn((text) => text),
    gray: vi.fn((text) => text),
  },
}))

vi.mock('ora', () => ({
  default: vi.fn(() => ({
    start: vi.fn().mockReturnThis(),
    succeed: vi.fn().mockReturnThis(),
    fail: vi.fn().mockReturnThis(),
  })),
}))

describe('CommandExecutor', () => {
  let executor: CommandExecutor

  let mockChildProcess: any

  beforeEach(() => {
    vi.clearAllMocks()
    executor = new CommandExecutor()

    // Mock child process
    mockChildProcess = {
      on: vi.fn(),
      stdout: { on: vi.fn() },
      stderr: { on: vi.fn() },
    }
    mockSpawn.mockReturnValue(mockChildProcess as any)
  })

  describe('execute', () => {
    test('should execute command successfully', async () => {
      // Setup mock to simulate successful execution
      mockChildProcess.on.mockImplementation((event: string, callback: (code: number) => void) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 10)
        }
      })

      const promise = executor.execute('echo', ['hello'])

      expect(mockSpawn).toHaveBeenCalledWith('echo', ['hello'], expect.any(Object))

      const result = await promise
      expect(result).toBe(0)
    })

    test('should handle command failure', async () => {
      // Setup mock to simulate failed execution
      mockChildProcess.on.mockImplementation((event: string, callback: (code: number) => void) => {
        if (event === 'close') {
          setTimeout(() => callback(1), 10)
        }
      })

      await expect(executor.execute('false')).rejects.toThrow()
    })
  })
})

describe('executeViteCommand', () => {
  beforeEach(() => {
    vi.clearAllMocks()

    // Mock child process for successful execution
    const mockChildProcess = {
      on: vi.fn((event, callback) => {
        if (event === 'close') {
          setTimeout(() => callback(0), 10)
        }
      }),
      stdout: { on: vi.fn() },
      stderr: { on: vi.fn() },
    }
    mockSpawn.mockReturnValue(mockChildProcess as any)
  })

  test('should execute vite command with default parameters', async () => {
    const environment = {
      name: 'dev',
      value: 'dev',
      description: '开发环境',
      mode: 'development',
    }

    const config = { environments: [environment] }

    await executeViteCommand(environment, config)

    expect(mockSpawn).toHaveBeenCalledWith(
      'npx',
      ['vite', '--mode', 'dev', '--host'],
      expect.objectContaining({ env: expect.objectContaining({ NODE_ENV: 'dev' }) }),
    )
  })

  test('should execute custom command with template variables', async () => {
    const environment = {
      name: 'test',
      value: 'test',
      description: '测试环境',
      mode: 'test',
      command: {
        cmd: 'npm',
        args: ['run', 'dev:{{environment.value}}', '--', '--port', '3000'],
      },
    }

    const config = { environments: [environment] }

    await executeViteCommand(environment, config)

    expect(mockSpawn).toHaveBeenCalledWith(
      'npm',
      ['run', 'dev:test', '--', '--port', '3000'],
      expect.objectContaining({ env: expect.objectContaining({ NODE_ENV: 'test' }) }),
    )
  })

  test('should replace multiple template variables', async () => {
    const environment = {
      name: 'production',
      value: 'prod',
      description: '生产环境',
      mode: 'production',
      command: {
        cmd: 'echo',
        args: ['{{environment.name}}', '{{environment.value}}', '{{environment.mode}}'],
      },
    }

    const config = { environments: [environment] }

    await executeViteCommand(environment, config)

    expect(mockSpawn).toHaveBeenCalledWith(
      'echo',
      ['production', 'prod', 'production'],
      expect.any(Object),
    )
  })

  test('should use defaultCommand when environment has no command', async () => {
    const environment = {
      name: 'staging',
      value: 'staging',
      description: '预发布环境',
      mode: 'staging',
      // 没有 command 属性
    }

    const config = {
      environments: [environment],
      defaultCommand: {
        cmd: 'npm',
        args: ['run', 'dev', '--mode', '{{environment.value}}'],
      },
    }

    await executeViteCommand(environment, config)

    expect(mockSpawn).toHaveBeenCalledWith(
      'npm',
      ['run', 'dev', '--mode', 'staging'],
      expect.objectContaining({ env: expect.objectContaining({ NODE_ENV: 'staging' }) }),
    )
  })

  test('should prioritize environment command over defaultCommand', async () => {
    const environment = {
      name: 'custom',
      value: 'custom',
      description: '自定义环境',
      mode: 'custom',
      command: {
        cmd: 'yarn',
        args: ['dev', '--mode', '{{environment.value}}'],
      },
    }

    const config = {
      environments: [environment],
      defaultCommand: {
        cmd: 'npm',
        args: ['run', 'dev'],
      },
    }

    await executeViteCommand(environment, config)

    // 应该使用环境自定义的命令，而不是 defaultCommand
    expect(mockSpawn).toHaveBeenCalledWith(
      'yarn',
      ['dev', '--mode', 'custom'],
      expect.any(Object),
    )
  })

  test('should use built-in default when no environment command and no defaultCommand', async () => {
    const environment = {
      name: 'fallback',
      value: 'fallback',
      description: '回退环境',
      mode: 'fallback',
      // 没有 command 属性
    }

    // 不传递 defaultCommand
    const config = { environments: [environment] }

    await executeViteCommand(environment, config)

    // 应该使用函数内置的默认命令
    expect(mockSpawn).toHaveBeenCalledWith(
      'npx',
      ['vite', '--mode', 'fallback', '--host'],
      expect.any(Object),
    )
  })

  test('should handle command execution errors', async () => {
    const environment = {
      name: 'error',
      value: 'error',
      description: 'Error environment',
      mode: 'development',
      command: {
        cmd: 'invalid-command',
        args: ['--fail'],
      },
    }

    // Mock spawn to simulate error
    const mockChildProcess: any = {
      on: vi.fn((event: string, callback: (code: number) => void): any => {
        if (event === 'close') {
          setTimeout(() => callback(1), 10) // Exit with error code
        }
        return mockChildProcess
      }),
      stdout: { on: vi.fn() },
      stderr: { on: vi.fn() },
    }
    mockSpawn.mockReturnValue(mockChildProcess as any)

    const config = { environments: [environment] }

    await expect(executeViteCommand(environment, config)).rejects.toThrow('命令执行失败: invalid-command --fail')

    expect(mockSpawn).toHaveBeenCalledWith(
      'invalid-command',
      ['--fail'],
      expect.any(Object),
    )
  })

  test('should handle spawn errors', async () => {
    const environment = {
      name: 'spawn-error',
      value: 'spawn-error',
      description: 'Spawn error environment',
      mode: 'development',
      command: {
        cmd: 'nonexistent-command',
        args: [],
      },
    }

    // Mock spawn to throw error
    mockSpawn.mockImplementation(() => {
      throw new Error('Command not found')
    })

    const config = { environments: [environment] }

    await expect(executeViteCommand(environment, config)).rejects.toThrow('Command not found')
  })
})
