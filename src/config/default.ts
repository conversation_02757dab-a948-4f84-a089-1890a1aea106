export interface Hook {
  name?: string;
  description?: string;
  type: 'command' | 'function';
  // 当 type 为 'command' 时使用
  cmd?: string;
  args?: string[];
  // 当 type 为 'function' 时使用
  fn?: (environment: Environment, config: Config) => Promise<void> | void;
}

export interface CommandConfig {
  cmd: string;
  args: string[];
}

export interface Environment {
  name: string;
  value: string;
  description: string;
  mode: string;
  // 支持不同类型的命令
  commands?: {
    start?: CommandConfig;
    build?: CommandConfig;
    [key: string]: CommandConfig | undefined;
  };
  // 向后兼容：如果没有指定 commands，使用 command 作为 start 命令
  command?: CommandConfig;
  // 环境级别的 hooks
  preHooks?: Hook[];
  postHooks?: Hook[];
}

export interface Config {
  environments: Environment[];
  // 支持不同类型的默认命令
  defaultCommands?: {
    start?: CommandConfig;
    build?: CommandConfig;
    [key: string]: CommandConfig | undefined;
  };
  // 向后兼容：如果没有指定 defaultCommands，使用 defaultCommand 作为 start 命令
  defaultCommand?: CommandConfig;
  // 全局级别的 hooks，会在所有环境执行前/后运行
  globalPreHooks?: Hook[];
  globalPostHooks?: Hook[];
}



export const defaultConfig: Config = {
  // 默认命令配置，支持不同的子命令
  defaultCommands: {
    start: {
      cmd: 'npx',
      args: ['vite', '--mode', '{{environment.value}}', '--host'],
    },
    build: {
      cmd: 'npx',
      args: ['vite', 'build', '--mode', '{{environment.value}}'],
    },
  },
  environments: [
    {
      name: 'dev',
      value: 'dev',
      description: '开发环境',
      mode: 'development',
      // 不需要定义 command，会使用 defaultCommand
    },
    {
      name: 'test',
      value: 'test',
      description: '测试环境',
      mode: 'test',
      // 不需要定义 command，会使用 defaultCommand
    },
    {
      name: 'hotfix',
      value: 'hotfix',
      description: '热修复环境',
      mode: 'hotfix',
      // 不需要定义 command，会使用 defaultCommand
    },
    {
      name: 'prod',
      value: 'prod',
      description: '生产环境',
      mode: 'production',
      // 不需要定义 command，会使用 defaultCommand
    },
  ],
}
