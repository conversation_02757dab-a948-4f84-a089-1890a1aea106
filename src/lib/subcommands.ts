import chalk from 'chalk'
import { Command } from 'commander'
import { readFileSync } from 'fs'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'
import {
  ConfigManager,
  EnvironmentManager,
  selectEnvironment,
} from '@/lib'
import { executeCommand } from './command-executor'

export type SubcommandType = 'start' | 'build'

export interface SubcommandOptions {
  api?: string;
  verbose?: boolean;
}

export class SubcommandManager {
  private program: Command

  constructor() {
    this.program = new Command()
    this.setupProgram()
  }

  private setupProgram(): void {
    // 读取 pilot-cli 库本身的 package.json 获取版本信息
    const currentFileUrl = import.meta.url
    const currentDir = dirname(fileURLToPath(currentFileUrl))
    // 从当前文件位置向上查找到项目根目录的 package.json
    // 构建后的文件在 dist/ 目录下，所以需要向上两级到达根目录
    const packageJsonPath = join(currentDir, '../../package.json')
    let packageJson
    try {
      packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf8'))
      console.log(`调试: 成功读取版本 ${packageJson.version} 从 ${packageJsonPath}`)
    } catch (error) {
      console.log(`调试: 读取失败 ${packageJsonPath}, 错误:`, error)
      // 如果读取失败，使用默认版本
      packageJson = { version: '1.0.0' }
    }

    this.program
      .name('pilot-cli')
      .description('Simple CLI tool for Vite development environments')
      .version(packageJson.version, '-v, --version', '显示版本信息')

    // 添加 start 子命令
    this.program
      .command('start')
      .description('启动开发服务器')
      .option('--api <env>', '指定环境 (dev|test|hotfix|prod)')
      .option('--verbose', '显示详细输出')
      .action(async (options: SubcommandOptions, _command) => {
        // 合并全局选项和子命令选项
        const globalOptions = this.program.opts()
        const mergedOptions = { ...globalOptions, ...options }
        await this.executeSubcommand('start', mergedOptions)
      })

    // 添加 build 子命令
    this.program
      .command('build')
      .description('构建项目')
      .option('--api <env>', '指定环境 (dev|test|hotfix|prod)')
      .option('--verbose', '显示详细输出')
      .action(async (options: SubcommandOptions, _command) => {
        // 合并全局选项和子命令选项
        const globalOptions = this.program.opts()
        const mergedOptions = { ...globalOptions, ...options }
        await this.executeSubcommand('build', mergedOptions)
      })

    // 全局选项（用于向后兼容）
    this.program
      .option('--api <env>', '指定环境 (dev|test|hotfix|prod)')
      .option('--verbose', '显示详细输出')

    this.program.addHelpText('after', `
示例:
  # 启动开发服务器
  pilot-cli start                       # 交互式选择环境并启动
  pilot-cli start --api=dev             # 直接启动开发环境
  pilot-cli --api=dev                   # 简写形式（默认 start）

  # 构建项目
  pilot-cli build                       # 交互式选择环境并构建
  pilot-cli build --api=prod            # 直接构建生产环境

  # 显示详细输出
  pilot-cli start --api=dev --verbose   # 启动时显示详细信息
      `)
  }

  async executeSubcommand(
    subcommand: SubcommandType,
    options: SubcommandOptions,
  ): Promise<void> {
    try {
      // 1. 加载配置
      const config = await ConfigManager.load()

      // 2. 创建环境管理器
      const environmentManager = new EnvironmentManager(config)

      // 3. 选择环境（交互式或直接指定）
      let environment
      if (options.api) {
        // CI模式：直接使用指定的环境
        environment = environmentManager.getByValue(options.api)
        if (!environment) {
          console.error(chalk.red(`错误: 不支持的环境 "${options.api}"`))
          console.log(
            chalk.yellow('可用环境:'),
            environmentManager.getValues().join(', '),
          )
          process.exit(1)
        }
        console.log(chalk.blue(`使用环境: ${environment.name}`))
      } else {
        // 交互式模式：让用户选择环境
        environment = await selectEnvironment(environmentManager.getAll())
      }

      // 4. 执行指定的子命令
      console.log(chalk.green(`🚀 ${this.getSubcommandDescription(subcommand)} ${environment.name} 环境...`))
      await executeCommand(subcommand, environment, config, { verbose: options.verbose || false })

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      console.error(
        chalk.red('错误:'),
        errorMessage,
      )

      // 提供友好的错误提示
      if (errorMessage.includes('ENOENT')) {
        console.log(chalk.yellow('提示: 请确保已安装相关依赖'))
        console.log(chalk.gray('运行: npm install 或 pnpm install'))
      }

      process.exit(1)
    }
  }

  private getSubcommandDescription(subcommand: SubcommandType): string {
    switch (subcommand) {
      case 'start':
        return '启动'
      case 'build':
        return '构建'
      default:
        return '执行'
    }
  }

  parse(argv: string[] = process.argv): void {
    // 如果没有指定子命令，且有 --api 参数，默认执行 start
    const args = argv.slice(2)
    const hasSubcommand = args.some((arg) => ['start', 'build'].includes(arg))
    const hasApiFlag = args.some((arg) => arg.startsWith('--api'))

    if (!hasSubcommand && hasApiFlag) {
      // 插入 start 子命令
      const newArgv = [...argv.slice(0, 2), 'start', ...args]
      this.program.parse(newArgv)
    } else {
      this.program.parse(argv)
    }
  }
}

// 创建全局实例
export const subcommandManager = new SubcommandManager()
