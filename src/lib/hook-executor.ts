/* eslint-disable @typescript-eslint/no-explicit-any */
import chalk from 'chalk'
import ora from 'ora'
import { CommandExecutor } from './command-executor'
import { VitePilotError, ErrorCodes } from './errors'
import type { Hook, Environment, Config } from '@/config'

export interface HookExecutorOptions {
  verbose?: boolean;
  cwd?: string;
}

export class HookExecutor {
  private commandExecutor: CommandExecutor
  private verbose: boolean

  constructor(options: HookExecutorOptions = {}) {
    this.verbose = options.verbose || false
    this.commandExecutor = new CommandExecutor({
      verbose: this.verbose,
      cwd: options.cwd,
    })
  }

  /**
   * 执行单个 hook
   */
  async executeHook(
    hook: Hook,
    environment: Environment,
    config: Config,
  ): Promise<void> {
    const hookName = hook.name || `${hook.type} hook`
    const description = hook.description || `执行 ${hookName}`

    if (this.verbose) {
      console.log(chalk.blue(`🪝 ${description}`))
    }

    try {
      switch (hook.type) {
        case 'command':
          await this.executeCommandHook(hook, environment, description)
          break
        case 'function':
          await this.executeFunctionHook(hook, environment, config, description)
          break
        default:
          throw new VitePilotError(
            `不支持的 hook 类型: ${(hook as any).type}`,
            ErrorCodes.CONFIG_INVALID,
          )
      }

      if (this.verbose) {
        console.log(chalk.green(`✅ ${description} - 完成`))
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error)
      console.error(chalk.red(`❌ ${description} - 失败: ${errorMsg}`))

      throw new VitePilotError(
        `Hook 执行失败: ${hookName}`,
        ErrorCodes.COMMAND_FAILED,
        { hook, originalError: error },
      )
    }
  }

  /**
   * 执行命令类型的 hook
   */
  private async executeCommandHook(
    hook: Hook,
    environment: Environment,
    description: string,
  ): Promise<void> {
    if (!hook.cmd) {
      throw new VitePilotError(
        'Command hook 必须指定 cmd 属性',
        ErrorCodes.CONFIG_INVALID,
      )
    }

    const args = hook.args || []

    // 替换命令参数中的模板变量
    const processedArgs = this.replaceTemplateVariables(args, environment)

    await this.commandExecutor.execute(
      hook.cmd,
      processedArgs,
      {
        description,
        env: { ...process.env, NODE_ENV: environment.value },
      },
    )
  }

  /**
   * 执行函数类型的 hook
   */
  private async executeFunctionHook(
    hook: Hook,
    environment: Environment,
    config: Config,
    description: string,
  ): Promise<void> {
    if (!hook.fn) {
      throw new VitePilotError(
        'Function hook 必须指定 fn 属性',
        ErrorCodes.CONFIG_INVALID,
      )
    }

    const spinner = this.verbose ? null : ora(description).start()

    try {
      await hook.fn(environment, config)

      if (spinner) {
        spinner.succeed(chalk.green(`✅ ${description}`))
      }
    } catch (error) {
      if (spinner) {
        spinner.fail(chalk.red(`❌ ${description}`))
      }
      throw error
    }
  }

  /**
   * 批量执行 hooks
   */
  async executeHooks(
    hooks: Hook[],
    environment: Environment,
    config: Config,
    phase: 'pre' | 'post' = 'pre',
  ): Promise<void> {
    if (!hooks || hooks.length === 0) {
      return
    }

    if (this.verbose) {
      console.log(chalk.cyan(`🔗 执行 ${phase} hooks (${hooks.length} 个)`))
    }

    for (const hook of hooks) {
      await this.executeHook(hook, environment, config)
    }

    if (this.verbose) {
      console.log(chalk.cyan(`✅ 所有 ${phase} hooks 执行完成`))
    }
  }

  /**
   * 替换命令参数中的模板变量
   */
  private replaceTemplateVariables(args: string[], environment: Environment): string[] {
    return args.map((arg) =>
      arg.replace(/\{\{environment\.value\}\}/g, environment.value)
        .replace(/\{\{environment\.name\}\}/g, environment.name)
        .replace(/\{\{environment\.mode\}\}/g, environment.mode)
        .replace(/\{\{environment\.description\}\}/g, environment.description))
  }
}
