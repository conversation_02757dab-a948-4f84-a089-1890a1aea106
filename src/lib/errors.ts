/**
 * 自定义错误类
 */
export class VitePilotError extends <PERSON>rror {
  public readonly code: string
  public readonly details: Record<string, unknown>

  constructor(message: string, code: string, details: Record<string, unknown> = {}) {
    super(message)
    this.name = 'VitePilotError'
    this.code = code
    this.details = details
  }
}

export const ErrorCodes = {
  CONFIG_NOT_FOUND: 'CONFIG_NOT_FOUND',
  CONFIG_INVALID: 'CONFIG_INVALID',
  PROJECT_NOT_DETECTED: 'PROJECT_NOT_DETECTED',
  COMMAND_FAILED: 'COMMAND_FAILED',
  INVALID_ARGUMENTS: 'INVALID_ARGUMENTS',
  VITE_CONFIG_NOT_FOUND: 'VITE_CONFIG_NOT_FOUND',
  DEPENDENCY_MISSING: 'DEPENDENCY_MISSING',
} as const

export type ErrorCode = typeof ErrorCodes[keyof typeof ErrorCodes]
