import { join } from 'path'
import { defaultConfig, type Config } from '@/config'
import { VitePilotError, ErrorCodes } from '@/lib/errors'
import chalk from 'chalk'

export class ConfigManager {
  /**
   * 加载配置文件
   * 优先级：vite.pilot.config.ts > pilot.config.js > 默认配置
   */
  static async load(searchFrom: string = process.cwd()): Promise<Config> {
    // 尝试加载外部配置文件
    const externalConfig = await this.loadExternalConfig(searchFrom)
    if (externalConfig) {
      return this.mergeConfigs(defaultConfig, externalConfig)
    }

    // 回退到默认配置，但仍需要通过 mergeConfigs 处理以确保环境有 command 属性
    console.log(chalk.blue('使用默认配置'))
    return this.mergeConfigs(defaultConfig, {})
  }

  /**
   * 加载外部配置文件 (pilot.config.ts/js)
   */
  private static async loadExternalConfig(searchFrom: string): Promise<Partial<Config> | null> {
    const configFiles = [
      'pilot.config.ts',
      'pilot.config.js',
      'pilot.config.mjs',
    ]

    for (const configFile of configFiles) {
      try {
        const configPath = join(searchFrom, configFile)
        console.log(chalk.blue(`尝试加载配置文件: ${configFile}`))

        const config = await import(configPath)
        const userConfig = config.default || config.config || config

        this.validate(userConfig)
        console.log(chalk.green(`✅ 成功加载配置文件: ${configFile}`))
        return userConfig
      } catch {
        // 继续尝试下一个配置文件
        continue
      }
    }

    return null
  }

  /**
   * 合并配置（外部配置覆盖默认配置）
   */
  private static mergeConfigs(defaultConfig: Config, userConfig: Partial<Config>): Config {
    const merged: Config = {
      environments: [...defaultConfig.environments],
      defaultCommand: defaultConfig.defaultCommand,
      defaultCommands: defaultConfig.defaultCommands,
      globalPreHooks: defaultConfig.globalPreHooks,
      globalPostHooks: defaultConfig.globalPostHooks,
    }

    // 合并 defaultCommand（向后兼容）
    if (userConfig.defaultCommand) {
      merged.defaultCommand = userConfig.defaultCommand
    }

    // 合并 defaultCommands
    if (userConfig.defaultCommands) {
      merged.defaultCommands = userConfig.defaultCommands
    }

    // 合并全局 hooks
    if (userConfig.globalPreHooks) {
      merged.globalPreHooks = userConfig.globalPreHooks
    }
    if (userConfig.globalPostHooks) {
      merged.globalPostHooks = userConfig.globalPostHooks
    }

    // 如果用户提供了环境配置，则完全替换默认环境配置
    if (userConfig.environments && Array.isArray(userConfig.environments)) {
      merged.environments = userConfig.environments.map((env) => ({
        ...env,
        // 优先级：环境自定义命令 > 用户配置的默认命令 > 内置默认命令
        command: env.command || merged.defaultCommand || {
          cmd: 'npx',
          args: ['vite', '--mode', '{{environment.value}}', '--host'],
        },
      }))
    }

    // 确保所有环境都有命令配置（包括默认环境）
    merged.environments = merged.environments.map((env) => ({
      ...env,
      command: env.command || merged.defaultCommand || {
        cmd: 'npx',
        args: ['vite', '--mode', '{{environment.value}}', '--host'],
      },
    }))

    return merged
  }



  /**
   * 验证配置格式
   */
  static validate(config: unknown): void {
    if (!config || typeof config !== 'object') {
      throw new VitePilotError(
        '配置文件必须导出一个对象',
        ErrorCodes.CONFIG_INVALID,
      )
    }

    const configObj = config as Record<string, unknown>

    // 验证环境配置
    if (configObj.environments) {
      if (!Array.isArray(configObj.environments)) {
        throw new VitePilotError(
          'environments 必须是数组',
          ErrorCodes.CONFIG_INVALID,
        )
      }

      configObj.environments.forEach((env: unknown, index: number) => {
        const envObj = env as Record<string, unknown>
        if (!envObj.name || !envObj.value || !envObj.description) {
          throw new VitePilotError(
            `环境配置 [${index}] 缺少必要字段: name, value, description`,
            ErrorCodes.CONFIG_INVALID,
          )
        }
      })
    }
  }

  /**
   * 获取默认配置
   */
  static getDefaultConfig(): Config {
    return { ...defaultConfig }
  }
}
