#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

echo "Creating docs directory..."
mkdir -p docs

echo "Copying GitHub CSS..."
cp ./node_modules/github-markdown-css/github-markdown.css docs/style.css

echo "Converting README.md to HTML..."
# Generate the HTML fragment from Markdown
pnpx markdown-it ./README.md -o docs/body.html

echo "Constructing final HTML document..."
# Create the final index.html
cat > docs/index.html <<EOL
<!DOCTYPE html>
<html>
<head>
  <meta charset='utf-8'>
  <meta name='viewport' content='width=device-width, initial-scale=1'>
  <link rel='stylesheet' href='style.css'>
  <style>
    body {
      padding: 1em 2em;
    }
  </style>
</head>
<body class="markdown-body">
EOL

# Append the HTML body content
cat docs/body.html >> docs/index.html

# Append the closing tags
cat >> docs/index.html <<EOL
</body>
</html>
EOL

# Clean up the intermediate file
rm docs/body.html

echo "Build complete. The site is ready to be deployed."
